package com.hntnbs.ai.survey.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntnbs.ai.survey.mapper.DynamicAgentMapper;
import com.hntnbs.ai.survey.model.DynamicAgentEntity;
import com.hntnbs.ai.survey.service.DynamicAgentService;
import org.springframework.stereotype.Service;

/**
 * 对话消息表(TEAiMessage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-06 18:33:56
 */
@Service("tEAiMessageService")
public class DynamicAgentServiceImpl extends ServiceImpl<DynamicAgentMapper, DynamicAgentEntity> implements DynamicAgentService {

    @Override
    public DynamicAgentEntity findByAgentName(String agentName) {
        return lambdaQuery().eq(DynamicAgentEntity::getAgentName, agentName).one();
    }
}

