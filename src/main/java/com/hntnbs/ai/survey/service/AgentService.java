package com.hntnbs.ai.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntnbs.ai.survey.model.DynamicAgentEntity;
import com.hntnbs.ai.survey.service.agent.base.BaseAgent;

import java.util.List;
import java.util.Map;

/**
 * 对话消息表(TEAiMessage)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-06 18:33:54
 */
public interface AgentService extends IService<DynamicAgentEntity> {

    DynamicAgentEntity findByAgentName(String agentName);

    List<DynamicAgentEntity> findAll();

    /**
     * 创建并返回一个可用的BaseAgent对象 类似于PlanningFactory中的createPlanningCoordinator方法
     * @param name 代理名称
     * @param planId 计划ID，用于标识代理所属的计划
     * @return 创建的BaseAgent对象
     */
    BaseAgent createDynamicBaseAgent(String name, String planId, Map<String, Object> initialAgentSetting);

}

