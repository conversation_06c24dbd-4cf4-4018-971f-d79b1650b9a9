package com.hntnbs.ai.survey.service.agent.recorders.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class UserInputWaitState implements Serializable {

	private String planId;

	private String message;

	private boolean waiting;

	private String formDescription; // 新增字段：表单描述

	private List<Map<String, String>> formInputs; // 新增字段：表单输入项

	public UserInputWaitState(String planId, String message, boolean waiting) {
		this.planId = planId;
		this.message = message;
		this.waiting = waiting;
	}

}