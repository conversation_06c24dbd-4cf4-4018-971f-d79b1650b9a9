package com.hntnbs.ai.survey.service.agent.loader;

import com.hntnbs.ai.survey.context.input.UserInputService;
import com.hntnbs.ai.survey.context.properties.ManusProperties;
import com.hntnbs.ai.survey.model.DynamicAgentEntity;
import com.hntnbs.ai.survey.service.AgentService;
import com.hntnbs.ai.survey.service.agent.base.LlmService;
import com.hntnbs.ai.survey.service.agent.recorders.PlanExecutionRecorder;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class DynamicAgentLoader {

	private final AgentService agentService;

	private final LlmService llmService;

	private final PlanExecutionRecorder recorder;

	private final ManusProperties properties;

	private final ToolCallingManager toolCallingManager;

	private final UserInputService userInputService;


	public DynamicAgentLoader(AgentService repository, @Lazy LlmService llmService,
                              PlanExecutionRecorder recorder, ManusProperties properties, @Lazy ToolCallingManager toolCallingManager, UserInputService userInputService) {
		this.agentService = repository;
		this.llmService = llmService;
		this.recorder = recorder;
		this.properties = properties;
		this.toolCallingManager = toolCallingManager;
        this.userInputService = userInputService;
    }

	public DynamicAgent loadAgent(String agentName, Map<String, Object> initialAgentSetting) {
		DynamicAgentEntity entity = agentService.findByAgentName(agentName);
		if (entity == null) {
			throw new IllegalArgumentException("Agent not found: " + agentName);
		}

		return new DynamicAgent(llmService, recorder, properties, entity.getAgentName(), entity.getAgentDescription(),
				entity.getNextStepPrompt(),
				Arrays.asList(entity.getAvailableToolKeys().split(",")),
				toolCallingManager,
				initialAgentSetting,
				userInputService);
	}


	public List<DynamicAgentEntity> getAllAgents() {
		return agentService.findAll();
	}

}