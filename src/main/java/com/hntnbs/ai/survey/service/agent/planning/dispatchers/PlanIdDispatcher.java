package com.hntnbs.ai.survey.service.agent.planning.dispatchers;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ID转换器，用于处理和转换planId
 */
@Component
@Slf4j
public class PlanIdDispatcher {

	// planId前缀常量
	private static final String PLAN_ID_PREFIX = "plan_";

	/**
	 * 检查ID是否为planId格式
	 * @param id 待检查的ID
	 * @return 如果是planId格式则返回true，否则返回false
	 */
	public boolean isPlanId(String id) {
		return id != null && id.startsWith(PLAN_ID_PREFIX);
	}


	/**
	 * 生成新的planId
	 * @return 新生成的planId
	 */
	public String generatePlanId() {
		String planId = PLAN_ID_PREFIX + IdWorker.getIdStr();
		log.debug("生成新的planId: {}", planId);
		return planId;
	}

}
