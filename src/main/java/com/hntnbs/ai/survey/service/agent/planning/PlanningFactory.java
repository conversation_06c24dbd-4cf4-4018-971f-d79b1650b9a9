package com.hntnbs.ai.survey.service.agent.planning;
import com.hntnbs.ai.survey.context.input.FormInputTool;
import com.hntnbs.ai.survey.context.properties.ManusProperties;
import com.hntnbs.ai.survey.model.DynamicAgentEntity;
import com.hntnbs.ai.survey.service.AgentService;
import com.hntnbs.ai.survey.service.agent.base.LlmService;
import com.hntnbs.ai.survey.service.agent.fun_tools.PlanningTool;
import com.hntnbs.ai.survey.service.agent.fun_tools.ToolCallBiFunctionDef;
import com.hntnbs.ai.survey.service.agent.fun_tools.def.DocLoaderTool;
import com.hntnbs.ai.survey.service.agent.loader.DynamicAgentLoader;
import com.hntnbs.ai.survey.service.agent.mcp.McpService;
import com.hntnbs.ai.survey.service.agent.mcp.McpServiceEntity;
import com.hntnbs.ai.survey.service.agent.mcp.McpStateHolderService;
import com.hntnbs.ai.survey.service.agent.mcp.McpTool;
import com.hntnbs.ai.survey.service.agent.planning.bo.PlanCreator;
import com.hntnbs.ai.survey.service.agent.planning.bo.PlanExecutor;
import com.hntnbs.ai.survey.service.agent.planning.bo.PlanFinalizer;
import com.hntnbs.ai.survey.service.agent.planning.dispatchers.PlanningCoordinator;
import com.hntnbs.ai.survey.service.agent.recorders.PlanExecutionRecorder;
import lombok.Getter;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.util.Timeout;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class PlanningFactory {

	private final PlanExecutionRecorder recorder;

	private final ManusProperties manusProperties;

	private final McpStateHolderService mcpStateHolderService;

	public PlanningFactory(ManusProperties manusProperties, PlanExecutionRecorder recorder, McpStateHolderService mcpStateHolderService) {
		this.recorder = recorder;
		this.manusProperties = manusProperties;
		this.mcpStateHolderService = mcpStateHolderService;
	}

	@Autowired
	@Lazy
	private LlmService llmService;

	@Autowired
	@Lazy
	private ToolCallingManager toolCallingManager;

	@Autowired
	private AgentService agentService;

	@Autowired
	private McpService mcpService;

	@Autowired
	private DynamicAgentLoader dynamicAgentLoader;

	public PlanningCoordinator createPlanningCoordinator(String planId) {

		List<DynamicAgentEntity> agentEntities = dynamicAgentLoader.getAllAgents();

		PlanningTool planningTool = new PlanningTool();

		PlanCreator planCreator = new PlanCreator(agentEntities, llmService, planningTool, recorder);
		PlanExecutor planExecutor = new PlanExecutor(agentEntities, recorder, agentService, llmService);
		PlanFinalizer planFinalizer = new PlanFinalizer(llmService, recorder);

        return new PlanningCoordinator(planCreator, planExecutor, planFinalizer);
	}


	@Bean
	public RestClient.Builder createRestClient() {
		// 1. 配置超时时间（单位：毫秒）
		int connectionTimeout = 600000; // 连接超时时间
		int readTimeout = 600000; // 响应读取超时时间
		int writeTimeout = 600000; // 请求写入超时时间

		// 2. 创建 RequestConfig 并设置超时
		RequestConfig requestConfig = RequestConfig.custom()
			.setConnectTimeout(Timeout.of(10, TimeUnit.MINUTES)) // 设置连接超时
			.setResponseTimeout(Timeout.of(10, TimeUnit.MINUTES))
			.setConnectionRequestTimeout(Timeout.of(10, TimeUnit.MINUTES))
			.build();

		// 3. 创建 CloseableHttpClient 并应用配置
		HttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();

		// 4. 使用 HttpComponentsClientHttpRequestFactory 包装 HttpClient
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

		// 5. 创建 RestClient 并设置请求工厂
		return RestClient.builder().requestFactory(requestFactory);
	}

	/**
	 * Provides an empty ToolCallbackProvider implementation when MCP is disabled
	 */
	@Bean
	@ConditionalOnMissingBean
	@ConditionalOnProperty(name = "spring.ai.mcp.client.enabled", havingValue = "false")
	public ToolCallbackProvider emptyToolCallbackProvider() {
		return () -> new ToolCallback[0];
	}

	public static class ToolCallBackContext {

		@Getter
        private final ToolCallback toolCallback;

		@Getter
        private final ToolCallBiFunctionDef functionInstance;



		public ToolCallBackContext(ToolCallback toolCallback, ToolCallBiFunctionDef functionInstance) {
			this.toolCallback = toolCallback;
			this.functionInstance = functionInstance;
		}

    }

	public Map<String, ToolCallBackContext> toolCallbackMap(String planId) {
		Map<String, ToolCallBackContext> toolCallbackMap = new HashMap<>();
		List<ToolCallBiFunctionDef> toolDefinitions = new ArrayList<>();

		// 添加所有工具定义
		toolDefinitions.add(new DocLoaderTool());
		toolDefinitions.add(new FormInputTool());
		List<McpServiceEntity> functionCallbacks = mcpService.getFunctionCallbacks(planId);
		for (McpServiceEntity toolCallback : functionCallbacks) {
			String serviceGroup = toolCallback.getServiceGroup();
			ToolCallback[] tCallbacks = toolCallback.getAsyncMcpToolCallbackProvider().getToolCallbacks();
			for (ToolCallback tCallback : tCallbacks) {
				// 这里的 serviceGroup 是工具的名称
				toolDefinitions.add(new McpTool(tCallback, serviceGroup, planId, mcpStateHolderService));
			}
		}

		// 为每个工具创建 FunctionToolCallback
		for (ToolCallBiFunctionDef toolDefinition : toolDefinitions) {
			FunctionToolCallback functionToolcallback = FunctionToolCallback
					.builder(toolDefinition.getName(), toolDefinition)
					.description(toolDefinition.getDescription())
					.inputSchema(toolDefinition.getParameters())
					.inputType(toolDefinition.getInputType())
					.toolMetadata(ToolMetadata.builder().returnDirect(toolDefinition.isReturnDirect()).build())
					.build();
			toolDefinition.setPlanId(planId);
			ToolCallBackContext functionToolcallbackContext = new ToolCallBackContext(functionToolcallback,
					toolDefinition);
			toolCallbackMap.put(toolDefinition.getName(), functionToolcallbackContext);
		}
		return toolCallbackMap;
	}


}
