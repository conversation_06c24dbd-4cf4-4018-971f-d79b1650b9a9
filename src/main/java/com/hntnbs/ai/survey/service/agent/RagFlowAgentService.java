package com.hntnbs.ai.survey.service.agent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hntnbs.ai.survey.dto.ExecuteAgentActionReq;
import com.hntnbs.ai.survey.dto.ExecuteAgentsReq;
import com.hntnbs.ai.survey.dto.SessionReq;
import com.hntnbs.ai.survey.model.TEAiConversation;
import com.hntnbs.ai.survey.model.TEAiMessage;
import com.hntnbs.ai.survey.service.TEAiConversationService;
import com.hntnbs.ai.survey.service.TEAiMessageService;
import com.hntnbs.ai.survey.utils.RagFlowClient;
import com.hntnbs.ai.survey.utils.handler.SseResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Transactional(rollbackFor = Exception.class, readOnly = true, propagation = Propagation.SUPPORTS)
@ConditionalOnProperty(name = "agent.type", havingValue = "ragflow", matchIfMissing = false)
@Slf4j
public class RagFlowAgentService implements IAgentService {

    @Value("${agent.id}")
    private String agentId;

    @Autowired
    private TEAiMessageService messageService;

    @Autowired
    private TEAiConversationService conversationService;

    @Override
    @Transactional
    public SseEmitter execute(ExecuteAgentActionReq request) {
        // 初始化SseEmitter，设置60分钟超时
        SseEmitter emitter = new SseEmitter(600_000L);

        // 处理sessionId
        RagFlowClient clientInstance;
        final String sessionId;
        if (StrUtil.isEmpty(request.sessionId())) {
            clientInstance = RagFlowClient.getClientInstance(agentId, request.env());
            sessionId = clientInstance.getSessionId();
            TEAiConversation conversation = new TEAiConversation(
                    sessionId,
                    agentId,
                    request.operatorEntity() != null ? request.operatorEntity().userId() : null,
                    new Date(),
                    new Date(),
                    0,
                    "新建会话",
                    0,
                    0,
                    request.env().getString("proId"),
                    "ragflow"
            );
            // 异步保存会话记录
            CompletableFuture.runAsync(() -> conversationService.save(conversation));
        } else {
            sessionId = request.sessionId();
            clientInstance = RagFlowClient.getClientInstance(agentId, sessionId);
        }

        // 保存用户消息
        TEAiMessage userMessage = new TEAiMessage();
        userMessage.setContent(request.question());
        userMessage.setConversationId(sessionId);
        userMessage.setId(IdWorker.getIdStr());
        userMessage.setRole("user");
        userMessage.setReference("");
        userMessage.setMessageId(IdWorker.getIdStr());
        CompletableFuture.runAsync(() -> messageService.save(userMessage));

        // 获取历史消息
        List<TEAiMessage> history = messageService.lambdaQuery()
                .eq(TEAiMessage::getConversationId, sessionId)
                .list();
        clientInstance.setHistoryMessages(JSONArray.copyOf(history));

        // 异步执行streamConversation
        StringBuilder builder = new StringBuilder();
        CompletableFuture.runAsync(() -> {
            try {
                clientInstance.streamConversation(
                        agentId,
                        request.question(),
                        request.env(),
                        new SseResponseHandler() {
                            @Override
                            public void handle(JSONObject data) {
                                try {
                                    String answer = Optional.ofNullable(data.getJSONObject("data"))
                                            .map(d -> d.getString("answer"))
                                            .orElse(data.getString("answer"));
                                    if (answer != null) {
                                        builder.append(answer);
                                        emitter.send(data); // 推送数据到前端
                                    }
                                } catch (IOException e) {
                                    emitter.completeWithError(e);
                                }
                            }

                            @Override
                            public void onError(Exception ex) {
                                emitter.completeWithError(ex);
                            }

                            @Override
                            public void onComplete() {
                                // 保存模型响应
                                TEAiMessage modelMessage = new TEAiMessage();
                                modelMessage.setContent(builder.toString());
                                modelMessage.setConversationId(sessionId);
                                modelMessage.setId(IdWorker.getIdStr());
                                modelMessage.setRole("assistant");
                                modelMessage.setReference("");
                                modelMessage.setMessageId(IdWorker.getIdStr());
                                CompletableFuture.runAsync(() -> messageService.save(modelMessage));
                                JSONObject sseEnd = JSONObject.parse("{\"code\":0,\"data\":true}");
                                try {
                                    emitter.send(sseEnd);/*遵循ragFlow的协议最后一个消息，data使用true来标志*/
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                                emitter.complete(); // 完成SSE连接
                            }
                        });
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
        });

        // 立即返回SseEmitter
        return emitter;
    }

    @Override
    public String executeAgents(ExecuteAgentsReq request) {
        String agents = request.agents();
        if (StrUtil.isEmpty(agents)){
            return null;
        }
        List<String> agentsList = Lists.newArrayList(agents.split(","));
        String paramsStr = request.params();
        List<String> params = Lists.newArrayList();
        /*这里要求需要传递的参数必须与要并发的agent数量要少，参数依次传递*/
        if (StrUtil.isNotBlank(paramsStr) && paramsStr.split(",").length > agentsList.size()){
            throw new RuntimeException("参数数量与agent数量不匹配");
        }
        if (StrUtil.isNotBlank(paramsStr)){
            params = Arrays.stream(paramsStr.split(",")).toList();
        }
        List<String> finalParams = params;
        AtomicInteger i = new AtomicInteger(-1);
        List<CompletableFuture<JSONObject>> futures = agentsList.stream()
                .map(agentId -> CompletableFuture.supplyAsync(() -> {
                    JSONObject req = new JSONObject();
                    req.put("proId", request.proId());
                    try {
                        RagFlowClient client = RagFlowClient.getNoSessionIdClient(req);
                        JSONObject res = client.conversation(agentId, request.question(), req, false, finalParams.get(i.incrementAndGet()));
                        log.info("分析完成：{}", res);
                        return res;
                    } catch (InterruptedException | IOException e) {
                        Thread.currentThread().interrupt(); // 恢复中断状态
                        throw new RuntimeException(e);
                    }
                }))
                .toList();
        // 等待所有任务完成并获取结果
        List<String> results = futures.stream()
                .map(m -> {
                    try {
                        JSONObject jsonObject = m.get();
                        log.info("获取的数据:{}", jsonObject);
                        if (jsonObject == null) return "";
                        JSONObject data = jsonObject.getJSONObject("data");
                        if (data == null) return "";
                        return data.getString("answer");
                    } catch (InterruptedException | ExecutionException e) {
                        throw new RuntimeException(e);
                    }
                })
                .toList();
        // 拼接结果
        return String.join("\n", results);
    }

    @Override
    public JSONObject selectListByUserId(SessionReq req) {
        String userId = req.operatorEntity().userId();
        String sessionId = req.sessionId();
        String proId = req.proId();
        List<TEAiConversation> list = conversationService.lambdaQuery()
                .eq(TEAiConversation::getUserId, userId)
                .eq(StrUtil.isNotEmpty(sessionId), TEAiConversation::getId, sessionId)
                .eq(TEAiConversation::getProId, proId)
                .isNotNull(TEAiConversation::getProId)
                .list();
        if (CollectionUtil.isEmpty(list)) return null;
        TEAiConversation conversation = list.getFirst();
        return RagFlowClient.getSessions(conversation.getAgentId(), conversation.getId());
    }

    @Override
    public void deleteSession(String userId, String sessionId) {
        return;
    }

    @Override
    public ResponseEntity<Map<String, Object>> executeAgentActionAsync(Map<String, String> request) {
        return null;
    }
}