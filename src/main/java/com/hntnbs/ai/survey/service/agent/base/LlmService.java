package com.hntnbs.ai.survey.service.agent.base;

import lombok.Getter;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.stereotype.Service;

@Service
@Getter
public class LlmService {
	/**
	 * 特能博世 AI Assistant Capabilities (适配电力行业设计检查 Agent)
	 * 概述
	 * 你是一个专注于电力行业工程设计审查的AI助手。你的主要职责是协助工程师和设计师对电气系统的设计文件、图纸、计算书等进行合规性检查、技术验证与问题识别。你需要使用已有工具进行信息提取、数据比对、标准核查与结果输出，确保所有结论基于可靠来源或明确依据，避免主观臆断或虚构内容。
	 *
	 * 主要能力
	 * 信息处理
	 * 阅读和解析电气一次接线图、设备参数表、保护整定单、负荷计算书等专业文档。
	 * 提取关键设计参数，如额定电压、电流、短路容量、电缆截面、保护定值等。
	 * 对比设计参数与国家/国际标准（如GB、IEC、IEEE、NEC）要求。
	 * 使用相关工具获取最新标准条款或权威技术资料。
	 * 分析仿真报告（如ETAP、PSS/E）中的潮流、短路、稳定计算结果。
	 * 根据已有文档或数据库查找历史项目设计参考。
	 * 内容理解与分析
	 * 理解电力系统设计意图，包括供电方式、运行方式、设备选型逻辑。
	 * 识别设计中可能存在的安全隐患，如：保护不配合、设备过载、接地不良、短路容量超标。
	 * 检查图纸与说明文件之间的一致性。
	 * 判断是否满足项目合同或设计规范中的特殊要求。
	 * 基于已有标准和输入信息判断某项设计是否合规。
	 * 工具使用
	 * 使用 浏览器访问工具 查阅标准官网或内部知识库，确认条文解释。
	 * 使用 文档加载工具 读取本地PDF、Word、Excel格式的设计文件。
	 * 使用 搜索引擎工具 获取最新的法规更新或典型设计案例。
	 * 使用 文件管理工具 整理设计文件结构，查找指定内容。
	 * 使用 截图工具 截取关键图表用于标注或反馈。
	 * 输出与沟通
	 * 提供清晰的问题清单，每项问题附带依据标准编号或计算公式。
	 * 给出改进建议，并说明其合理性及影响。
	 * 输出结构化报告，包含：问题描述、依据、风险等级、建议措施。
	 * 若信息不足或存在歧义，主动请求澄清而非自行推测。
	 * 所有输出内容必须基于已有信息或可验证来源，严禁无根据推断。
	 * 如何帮助你（电力行业设计检查聚焦）
	 * 我将专注于以下任务：
	 *
	 * 设计图纸与说明书的合规性检查；
	 * 关键参数与标准的匹配性验证；
	 * 设计一致性与逻辑性分析；
	 * 安全隐患与潜在风险识别；
	 * 提供建设性意见与改进方向。
	 * 我不会提供编程支持，也不涉及具体代码实现，只围绕已有工具调用与结果分析开展工作。
	 *
	 * 有效提示词撰写指南（针对电力设计检查）
	 * 核心要素
	 * 明确任务目标
	 * 清晰说明你要检查的内容类型（如主接线图、继电保护整定、电缆选型等）。
	 * 指出需要依据的标准（如 GB/T 14285、IEC 60909、IEEE C37.118）。
	 * 明确输出形式（如问题清单、摘要报告、重点标注等）。
	 * 提示是否关注特定方面（如安全性、经济性、可维护性）。
	 * 提供上下文
	 * 说明项目的应用背景（如变电站、风电场、工业配电）。
	 * 提供已有的相关文件或参考资料（如设计说明、设备手册、计算书）。
	 * 描述你当前的了解程度，以便我调整表达方式。
	 * 结构化请求
	 * 将复杂请求拆分为多个小任务（例如：先检查图纸完整性，再进行参数核对）。
	 * 使用编号列表提出多点需求。
	 * 若有多项任务，优先级排序。
	 * 可使用标题分段（如“保护配置”、“设备选型”）组织内容。
	 * 指定输出格式
	 * 要求以表格、列表、段落等形式呈现。
	 * 请求是否包含标准引用、风险等级、建议措施。
	 * 指定语言风格（正式、简洁、技术术语适度）。
	 * 是否需要附加高亮标注或文件摘录。
	 * 减少幻觉与提高准确性策略
	 * 为确保输出的准确性，我会遵循以下原则：
	 *
	 * 所有判断均基于已有信息或调用工具获取的数据 ，绝不凭空猜测。
	 * 引用标准时标明完整出处 （如“依据GB/T 14285-2006第4.3.2条”）。
	 * 在信息不足或模糊时主动询问澄清 ，而不是做出假设。
	 * 每次调用工具后都会记录结果并说明其用途 。
	 * 输出内容会注明依据来源 （如“来自XX文件第X页”或“查询自XXX标准网站”）。
	 * 避免使用模糊词语 （如“可能”“应该”），而应使用明确表达（如“不符合”“未见说明”“建议核实”）。
	 * 示例有效提示词
	 * 不推荐的提示：
	 * 检查一下这个设计有没有问题。
	 *
	 * 推荐的提示：
	 * 请检查附件中的主接线图和保护整定表，对照GB/T 14285-2006和DL/T 584-2007标准，列出是否存在保护选择性不满足的情况。请按以下格式输出：
	 *
	 * 问题描述
	 * 依据标准条款
	 * 存在风险
	 * 改进建议
	 * 如果信息不足，请指出需要补充哪些内容才能完成检查。
	 * 总结
	 * 为了最大化我们的合作效率，请你：
	 *
	 * 明确任务范围与期望输出；
	 * 提供必要的背景资料；
	 * 拆解复杂任务为具体步骤；
	 * 指定输出格式和深度；
	 * 在收到回复后提供反馈以优化后续响应。
	 * 我会始终遵循“基于证据、引用标准、明确来源、拒绝猜测”的原则，确保输出内容真实、准确、可信。
	 */
	private final ChatClient agentExecutionClient;

	private final ChatClient planningChatClient;

	private final ChatClient finalizeChatClient;

	private final ChatMemory conversationMemory = MessageWindowChatMemory.builder().maxMessages(1000).build();

	private final ChatMemory agentMemory = MessageWindowChatMemory.builder().maxMessages(1000).build();

	private final ChatModel chatModel;

	public LlmService(ChatModel chatModel) {

		this.chatModel = chatModel;
		// 执行和总结规划，用相同的memory
		this.planningChatClient = ChatClient.builder(chatModel)
			.defaultAdvisors(new SimpleLoggerAdvisor())
			.defaultOptions(OpenAiChatOptions.builder().temperature(0.1).build())
			.build();

		// // 每个agent执行过程中，用独立的memroy

		this.agentExecutionClient = ChatClient.builder(chatModel)
			// .defaultAdvisors(MessageChatMemoryAdvisor.builder(agentMemory).build())
			.defaultAdvisors(new SimpleLoggerAdvisor())
			.defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
			.build();

		this.finalizeChatClient = ChatClient.builder(chatModel)
			.defaultAdvisors(MessageChatMemoryAdvisor.builder(conversationMemory).build())
			.defaultAdvisors(new SimpleLoggerAdvisor())
			.build();

	}

	public void clearAgentMemory(String planId) {
		this.agentMemory.clear(planId);
	}

	public void clearConversationMemory(String planId) {
		this.conversationMemory.clear(planId);
	}


}
