package com.hntnbs.ai.survey.service.agent;

import com.alibaba.fastjson2.JSONObject;
import com.hntnbs.ai.survey.dto.ExecuteAgentActionReq;
import com.hntnbs.ai.survey.dto.ExecuteAgentsReq;
import com.hntnbs.ai.survey.dto.SessionReq;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

public interface IAgentService {

    /**
     * 执行agent任务
     * @param request
     * @return
     */
    SseEmitter execute(ExecuteAgentActionReq request);

    /**
     * 执行多个agent任务
     * @param request
     * @return
     */
    String executeAgents(ExecuteAgentsReq request);

    JSONObject selectListByUserId(SessionReq req);

    void deleteSession(String userId, String sessionId);

    /**
     * 异步执行agent任务
     * @param request
     * @return
     */
    ResponseEntity<Map<String, Object>> executeAgentActionAsync(Map<String, String> request);
}
