package com.hntnbs.ai.survey.controller.exception;

import com.hntnbs.ai.survey.controller.exception.ex.AgentActionException;
import com.hntnbs.ai.survey.controller.exception.ex.BusinessException;
import com.hntnbs.ai.survey.controller.exception.ex.MCPActionException;
import com.hntnbs.ai.survey.exception.ConversationNotFoundException;
import com.hntnbs.ai.survey.exception.DifyClientException;
import com.hntnbs.ai.survey.exception.DifyFlowException;
import com.hntnbs.ai.survey.exception.DifyServerException;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

// 全局异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    public record ErrorResponse(String code, String message, Map<String, String> details) {
    }

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    // 处理自定义业务异常
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException ex) {
        logger.error("业务错误: {}", ex.getMessage(), ex);
        ErrorResponse response = new ErrorResponse(ex.getCode(), ex.getMessage(), null);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    // 处理 Dify API 相关异常
    @ExceptionHandler(DifyFlowException.class)
    public ResponseEntity<ErrorResponse> handleDifyFlowException(DifyFlowException ex) {
        logger.error("Dify API 错误: {}", ex.getMessage(), ex);
        String errorCode = "DIFY_ERROR";
        HttpStatus status;

        if (ex instanceof ConversationNotFoundException) {
            status = HttpStatus.NOT_FOUND;
            errorCode = "CONVERSATION_NOT_FOUND";
        } else if (ex instanceof DifyClientException) {
            status = HttpStatus.BAD_REQUEST;
            errorCode = ex.getMessage().split(":")[0]; // 提取错误码，如 "invalid_param"
        } else if (ex instanceof DifyServerException) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            errorCode = "INTERNAL_SERVER_ERROR";
        } else {
            status = HttpStatus.INTERNAL_SERVER_ERROR; // 默认处理未知的 DifyFlowException
        }

        ErrorResponse response = new ErrorResponse(errorCode, ex.getMessage(), null);
        return new ResponseEntity<>(response, status);
    }

    // 处理参数验证异常 (@Valid 注解触发)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(MethodArgumentNotValidException ex) {
        logger.warn("参数验证错误: {}", ex.getMessage());
        Map<String, String> details = new HashMap<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            details.put(error.getField(), error.getDefaultMessage());
        }
        ErrorResponse response = new ErrorResponse("VALIDATION_ERROR", "输入参数无效", details);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    // 处理 ConstraintViolationException（路径参数或查询参数验证）
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponse> handleConstraintViolationException(ConstraintViolationException ex) {
        logger.warn("约束验证错误: {}", ex.getMessage());
        Map<String, String> details = new HashMap<>();
        ex.getConstraintViolations().forEach(violation ->
                details.put(violation.getPropertyPath().toString(), violation.getMessage()));
        ErrorResponse response = new ErrorResponse("VALIDATION_ERROR", "输入参数无效", details);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    // 处理 JSON 解析异常
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        logger.warn("JSON 解析错误: {}", ex.getMessage());
        ErrorResponse response = new ErrorResponse("JSON_PARSE_ERROR", "JSON 格式无效", null);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    // 处理 agent内部 执行异常
    @ExceptionHandler(AgentActionException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(AgentActionException ex) {
        logger.warn("agent 执行异常: {}", ex.getMessage());
        ErrorResponse response = new ErrorResponse("AGENT_ACTION_ERROR", "agent 执行异常", null);
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // 处理 mcp 执行异常
    @ExceptionHandler(MCPActionException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(MCPActionException ex) {
        logger.warn("mcp 执行异常: {}", ex.getMessage());
        ErrorResponse response = new ErrorResponse("MCP_ACTION_ERROR", "mcp 执行异常", null);
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // 处理其他未捕获的异常
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
        logger.error("未预期错误: {}", ex.getMessage(), ex);
        ErrorResponse response = new ErrorResponse("INTERNAL_SERVER_ERROR", "服务器内部错误: " + ex.getMessage(), null);
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}