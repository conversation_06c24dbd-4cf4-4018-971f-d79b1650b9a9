package com.hntnbs.ai.survey.controller;

import com.alibaba.fastjson2.JSONObject;
import com.hntnbs.ai.survey.context.ExecutionContext;
import com.hntnbs.ai.survey.dto.ExecuteAgentActionReq;
import com.hntnbs.ai.survey.dto.ExecuteAgentsReq;
import com.hntnbs.ai.survey.service.agent.IAgentService;
import com.hntnbs.ai.survey.service.agent.planning.PlanningFactory;
import com.hntnbs.ai.survey.service.agent.planning.dispatchers.PlanIdDispatcher;
import com.hntnbs.ai.survey.service.agent.planning.dispatchers.PlanningCoordinator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/v1/agent")
@Slf4j
public class AgentController {

    @Autowired
    private IAgentService agentService;

    @PostMapping("/execute")
    public SseEmitter executeAgentAction(@RequestBody ExecuteAgentActionReq request) {
        return agentService.execute(request);
    }

    @Autowired
    private PlanIdDispatcher planIdDispatcher;

    @Autowired
    @Lazy
    private PlanningFactory planningFactory;

    @PostMapping("/executeAsync")
    public ResponseEntity<Map<String, Object>> executeAgentActionAsync(@RequestBody Map<String, String> request) {
        String query = request.get("query");
        if (query == null || query.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "用户问题不能为空"));
        }
        ExecutionContext context = new ExecutionContext();
        context.setUserRequest(query);
        // 使用 PlanIdDispatcher 生成唯一的计划ID
        String planId = planIdDispatcher.generatePlanId();
        context.setPlanId(planId);
        context.setNeedSummary(true);
        // 获取或创建规划流程
        PlanningCoordinator planningFlow = planningFactory.createPlanningCoordinator(planId);

        // 异步执行任务
        CompletableFuture.supplyAsync(() -> {
            try {
                return planningFlow.executePlan(context);
            }
            catch (Exception e) {
                log.error("执行计划失败", e);
                throw new RuntimeException("执行计划失败: " + e.getMessage(), e);
            }
        });

        // 返回任务ID及初始状态
        Map<String, Object> response = new HashMap<>();
        response.put("planId", planId);
        response.put("status", "processing");
        response.put("message", "任务已提交，正在处理中");

        return ResponseEntity.ok(response);
    }


    @PostMapping("/executeConcurrent")
    public ResponseEntity<String> executeConcurrent(@RequestBody ExecuteAgentsReq request) {
        log.info("并发agent请求: {}", JSONObject.toJSONString(request));
        return ResponseEntity.ok(agentService.executeAgents(request));
    }


}