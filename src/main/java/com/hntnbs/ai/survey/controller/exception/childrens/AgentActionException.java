import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @projectName:    tnbs-ai-client 
 * @package:        com.hntnbs.ai.survey.controller.exception.childrens
 * @className:      AgentActionException
 * @author:     jk
 * @description:  TODO  
 * @date:    2025/6/23 15:57
 * @version:    1.1
 */ 
@Data
@ApiModel()
public class AgentActionException {

    @ApiModelProperty("名称")
    private String name;
    
    @ApiModelProperty("id")
    private String id;
}
