package com.hntnbs.ai.survey.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_e_ai_mcp_config")
public class McpConfigEntity {

	@TableId(value = "id")
	private Long id;

	@TableField(value = "mcp_server_name")
	private String mcpServerName;

	@TableField(value = "connection_type")
	private String connectionType;

	@TableField(value = "connection_config")
	private String connectionConfig;

}