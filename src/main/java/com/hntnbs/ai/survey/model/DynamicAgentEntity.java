package com.hntnbs.ai.survey.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_e_ai_dynamic_agent")
public class DynamicAgentEntity {

	private String id;

	private String agentName;

	private String agentDescription;

	private String systemPrompt;

	private String nextStepPrompt;

	private String deleteFlag;

	/**
	 * 平台的不同要求不同，这里可能是agent_id, api_key, tool_key, 或者其他可以标志唯一的agent的参数id,有多个时使用逗号隔开
	 */
	private String availableToolKeys;

	private String className;

}