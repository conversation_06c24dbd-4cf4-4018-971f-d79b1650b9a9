package com.hntnbs.ai.survey.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntnbs.ai.survey.config.options.ConfigInputType;
import lombok.*;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_e_ai_config")
public class ConfigEntity {

	private String id;

	/**
	 * 配置组
	 */
	private String configGroup;

	/**
	 * 配置子组
	 */
	private String configSubGroup;

	/**
	 * 配置键
	 */
	private String configKey;

	/**
	 * 配置项完整路径
	 */
	private String configPath;

	/**
	 * 配置值
	 */
	private String configValue;

	/**
	 * 默认值
	 */
	private String defaultValue;

	/**
	 * 配置描述
	 */
	private String description;

	/**
	 * 输入类型
	 */
	private ConfigInputType inputType;

	/**
	 * 选项JSON字符串 用于存储SELECT类型的选项数据
	 */
	private String optionsJson;

	/**
	 * 最后更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	protected void onCreate() {
		createTime = LocalDateTime.now();
		updateTime = LocalDateTime.now();
	}

	protected void onUpdate() {
		updateTime = LocalDateTime.now();
	}
}
