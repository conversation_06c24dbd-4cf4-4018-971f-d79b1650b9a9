package com.hntnbs.ai.survey.context.input;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hntnbs.ai.survey.service.agent.fun_tools.ToolCallBiFunctionDef;
import com.hntnbs.ai.survey.service.agent.fun_tools.ToolExecuteResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LLM表单输入工具：支持带标签的多输入项和描述说明。
 */
public class FormInputTool implements ToolCallBiFunctionDef {

    private static final Logger log = LoggerFactory.getLogger(FormInputTool.class);

    private static final String PARAMETERS = """
            {
              "type": "object",
              "properties": {
                "inputs": {
                  "type": "array",
                  "description": "输入项列表，每项包含 label 和 value 字段",
                  "items": {
                    "type": "object",
                    "properties": {
                      "label": { "type": "string", "description": "输入项标签" },
                      "value": { "type": "string", "description": "输入内容" }
                    },
                    "required": ["label"]
                  }
                },
                "description": {
                  "type": "string",
                  "description": "如何填写这些输入项的说明"
                }
              },
              "required": [ "description"]
            }
            """;

    public static final String name = "form_input";

    private static final String description = """
            提供一个带标签的多输入项表单工具。
            
            LLM可通过本工具 让用户 提交0个或多个输入项（每项有label和内容），并附带填写说明。
            允许用户提交0个输入项。
            适用于需要结构化输入的场景也可以用于模型需要等待用户输入然后再继续的场景.
            """;

    public static OpenAiApi.FunctionTool getToolDefinition() {
        OpenAiApi.FunctionTool.Function function = new OpenAiApi.FunctionTool.Function(description, name, PARAMETERS);
        return new OpenAiApi.FunctionTool(function);
    }

    public static FunctionToolCallback<String, ToolExecuteResult> getFunctionToolCallback() {
        return FunctionToolCallback.builder(name, new FormInputTool())
                .description(description)
                .inputSchema(PARAMETERS)
                .inputType(String.class)
                .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
                .build();
    }

    // Data structures:

    /**
     * 表单输入项，包含标签和对应的值。
     */
    @Data
    @AllArgsConstructor
    public static class InputItem {

        private String label;

        private String value;

    }

    /**
     * 用户提交的表单数据，包含输入项列表和说明。
     */
    @Data
    @AllArgsConstructor
    public static class UserFormInput {

        private List<InputItem> inputs;

        private String description;

    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public enum InputState {

        AWAITING_USER_INPUT, INPUT_RECEIVED, INPUT_TIMEOUT

    }

    @Setter
    @Getter
    private InputState inputState = InputState.INPUT_RECEIVED; // Default state

    private UserFormInput currentFormDefinition; // Stores the form structure defined by
    // LLM and its current values

    @Override
    public ToolExecuteResult apply(String s, ToolContext toolContext) {
        log.info("FormInputTool input: {}", s);
        try {
            this.currentFormDefinition = objectMapper.readValue(s, UserFormInput.class);
            // Initialize values to empty string if null, to ensure they are present for
            // form binding
            if (this.currentFormDefinition != null && this.currentFormDefinition.getInputs() != null) {
                for (InputItem item : this.currentFormDefinition.getInputs()) {
                    if (item.getValue() == null) {
                        item.setValue(""); // Initialize with empty string
                    }
                }
            }
            setInputState(InputState.AWAITING_USER_INPUT);
            // Return the original JSON string 's' which represents the form definition.
            // The agent can use this or call getLatestUserFormInput() via
            // UserInputService.
            return new ToolExecuteResult(s);
        } catch (IOException e) {
            log.error("Error deserializing form input JSON: {}. Error: {}", s, e.getMessage());
            // Do not change state to AWAITING_USER_INPUT if parsing fails.
            // Keep previous state or reset to INPUT_RECEIVED.
            // this.inputState = InputState.INPUT_RECEIVED; // Or handle error state
            // appropriately
            this.currentFormDefinition = null; // Clear partially parsed/invalid form
            return new ToolExecuteResult("{\"error\": \"Failed to parse form input: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 获取由LLM定义的最新表单结构（包括描述和输入项标签及当前值）。 这个表单结构将用于在前端呈现给用户。
     *
     * @return 最新的 UserFormInput 对象，如果尚未定义则为 null。
     */
    public UserFormInput getLatestUserFormInput() {
        return this.currentFormDefinition;
    }

    /**
     * 设置用户提交的表单输入值。 这些值将更新 currentFormDefinition 中对应输入项的 value。
     *
     * @param submittedItems 用户提交的输入项列表 (label-value pairs).
     */
    public void setUserFormInputValues(List<InputItem> submittedItems) {
        if (this.currentFormDefinition == null || this.currentFormDefinition.getInputs() == null) {
            log.warn("Cannot set user form input values: form definition is missing or has no inputs.");
            return;
        }
        if (submittedItems == null) {
            log.warn("Submitted items are null. No values to update.");
            return;
        }

        Map<String, String> submittedValuesMap = new HashMap<>();
        for (InputItem submittedItem : submittedItems) {
            if (submittedItem.getLabel() != null) {
                submittedValuesMap.put(submittedItem.getLabel(), submittedItem.getValue());
            }
        }

        for (InputItem definitionItem : this.currentFormDefinition.getInputs()) {
            if (definitionItem.getLabel() != null && submittedValuesMap.containsKey(definitionItem.getLabel())) {
                definitionItem.setValue(submittedValuesMap.get(definitionItem.getLabel()));
            }
        }
        // The caller (UserInputService) is responsible for calling
        // markUserInputReceived()
    }

    public void markUserInputReceived() {
        setInputState(InputState.INPUT_RECEIVED);
    }

    public void handleInputTimeout() {
        log.warn("Input timeout occurred. No input received from the user for form: {}",
                this.currentFormDefinition != null ? this.currentFormDefinition.getDescription() : "N/A");
        setInputState(InputState.INPUT_TIMEOUT);
        this.currentFormDefinition = null; // Clear form definition on timeout
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getParameters() {
        return PARAMETERS;
    }

    @Override
    public Class<?> getInputType() {
        return String.class;
    }

    @Override
    public boolean isReturnDirect() {
        return true;
    }

    @Override
    public void setPlanId(String planId) {
        // 可选实现
    }

    @Override
    public void cleanup(String planId) {
        // 可选实现
    }

    @Override
    public String getServiceGroup() {
        return "default-service-group";
    }

    /**
     * 获取当前工具状态，包括表单说明和输入项 (包括用户已输入的值 if any)
     */
    @Override
    public String getCurrentToolStateString() {
        if (currentFormDefinition == null) {
            return String.format("FormInputTool 状态：未定义表单。当前输入状态: %s", inputState.toString());
        }
        try {
            return "FormInputTool 状态：\n" + String.format("说明：%s\n输入项：%s\n", currentFormDefinition.getDescription(),
                    objectMapper.writeValueAsString(currentFormDefinition.getInputs())) +
                    String.format("当前输入状态: %s\n", inputState.toString());
        } catch (JsonProcessingException e) {
            log.error("Error serializing currentFormDefinition for state string", e);
            return String.format("FormInputTool 状态：序列化输入项时出错。当前输入状态: %s", inputState.toString());
        }
    }

}