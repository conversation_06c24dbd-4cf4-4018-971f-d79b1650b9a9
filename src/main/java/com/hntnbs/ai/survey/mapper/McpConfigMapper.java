package com.hntnbs.ai.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntnbs.ai.survey.model.McpConfigEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface McpConfigMapper extends BaseMapper<McpConfigEntity> {

    @Select("SELECT * FROM t_e_ai_mcp_config where delete_flag = '0'")
    List<McpConfigEntity> findAll();

    @Select("SELECT * FROM t_e_ai_mcp_config where mcp_server_name = #{serverName} and delete_flag = '0'")
    McpConfigEntity findByMcpServerName(String serverName);

    @Insert("INSERT INTO t_e_ai_mcp_config (mcp_server_name, connection_config, connection_type, create_time, update_time, delete_flag) VALUES (#{mcpServerName}, #{connectionConfig}, #{connectionType}, #{createTime}, #{updateTime}, '0')")
    McpConfigEntity save(McpConfigEntity mcpConfigEntity);
}
