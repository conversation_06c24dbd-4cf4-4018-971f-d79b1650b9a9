package com.hntnbs.ai.survey.config;

import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义OpenAI配置，用于支持Qwen3模型的enable_thinking参数
 */
@Configuration
public class CustomOpenAiConfig {

    @Value("${spring.ai.openai.api-key}")
    private String apiKey;

    @Value("${spring.ai.openai.base-url}")
    private String baseUrl;

    @Value("${spring.ai.openai.chat.options.model}")
    private String model;

    /**
     * 创建自定义的OpenAI API客户端，支持额外的参数
     */
    @Bean
    @Primary
    public ChatModel customOpenAiChatModel() {
        // 创建OpenAI API客户端
        OpenAiApi openAiApi = OpenAiApi.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .build();

        // 创建聊天选项，包含enable_thinking=false参数
        OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
                .model(model)
                .temperature(0.1)
                .build();

        // 创建自定义的ChatModel
        return new CustomOpenAiChatModel(openAiApi, chatOptions);
    }
}
