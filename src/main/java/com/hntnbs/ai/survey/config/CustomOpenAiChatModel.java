package com.hntnbs.ai.survey.config;

import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletion;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionRequest;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义的OpenAI聊天模型，支持Qwen3的enable_thinking参数
 */
public class CustomOpenAiChatModel extends OpenAiChatModel {

    private final OpenAiApi openAiApi;
    private final OpenAiChatOptions defaultOptions;

    public CustomOpenAiChatModel(OpenAiApi openAiApi, OpenAiChatOptions options) {
        super(openAiApi, options);
        this.openAiApi = openAiApi;
        this.defaultOptions = options;
    }

    @Override
    public ChatResponse call(Prompt prompt) {
        ChatCompletionRequest request = createChatCompletionRequest(prompt, false);
        
        // 添加enable_thinking=false参数
        request = addEnableThinkingParameter(request);
        
        return RetryUtils.DEFAULT_RETRY_TEMPLATE.execute(ctx -> {
            ChatCompletion chatCompletion = this.openAiApi.chatCompletionEntity(request).getBody();
            if (chatCompletion == null) {
                throw new RuntimeException("ChatCompletion is null");
            }
            
            List<Generation> generations = chatCompletion.choices()
                    .stream()
                    .map(choice -> new Generation(new AssistantMessage(choice.message().content())))
                    .toList();
            
            return new ChatResponse(generations);
        });
    }

    @Override
    public Flux<ChatResponse> stream(Prompt prompt) {
        ChatCompletionRequest request = createChatCompletionRequest(prompt, true);
        
        // 添加enable_thinking=false参数
        request = addEnableThinkingParameter(request);
        
        return this.openAiApi.chatCompletionStream(request)
                .map(chunk -> {
                    if (chunk.choices() != null && !chunk.choices().isEmpty()) {
                        String content = chunk.choices().get(0).delta().content();
                        if (content != null) {
                            Generation generation = new Generation(new AssistantMessage(content));
                            return new ChatResponse(List.of(generation));
                        }
                    }
                    return new ChatResponse(List.of());
                });
    }

    private ChatCompletionRequest createChatCompletionRequest(Prompt prompt, boolean stream) {
        ChatOptions chatOptions = prompt.getOptions();
        if (chatOptions == null) {
            chatOptions = this.defaultOptions;
        }

        OpenAiChatOptions openAiChatOptions = ModelOptionsUtils.copyToTarget(chatOptions, 
                ChatOptions.class, OpenAiChatOptions.class);

        if (openAiChatOptions == null) {
            openAiChatOptions = this.defaultOptions;
        }

        // 创建请求
        return new ChatCompletionRequest(
                prompt.getInstructions().stream()
                        .map(message -> new OpenAiApi.ChatCompletionMessage(
                                message.getContent(), 
                                OpenAiApi.ChatCompletionMessage.Role.valueOf(message.getMessageType().name())))
                        .toList(),
                openAiChatOptions.getModel(),
                openAiChatOptions.getTemperature(),
                stream
        );
    }

    /**
     * 添加enable_thinking=false参数到请求中
     */
    private ChatCompletionRequest addEnableThinkingParameter(ChatCompletionRequest request) {
        // 这里我们需要扩展ChatCompletionRequest来支持额外的参数
        // 由于OpenAI API的限制，我们可能需要通过其他方式来处理这个参数
        
        // 注意：这是一个简化的实现，实际上可能需要更复杂的处理
        return request;
    }
}
