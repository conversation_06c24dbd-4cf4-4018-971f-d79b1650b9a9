debug: true

spring:
  application:
    name: tnbs_survey_client

  datasource:
    username: root
    password: p@ssw0rd
    url: **********************************************************************************************************************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource

  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: public
        enabled: false
      config:
        enabled: false
        import-check:
          enabled: false

  config:
    import: optional:nacos:tnbs_survey_client.properties?namespace=public

  ai:
    openai:
      api-key: sk-5KLF9TlyStmL3-jf-6yX3Q
      base-url: http://*************:32101
      chat:
        options:
          model: o-qwen3-235b-a22b
          enable-thinking: false
      embedding:
        base-url: http://*************:32101
        api-key: sk-5KLF9TlyStmL3-jf-6yX3Q
        options:
          model: o-qwen3/text-embedding-v4
    vectorstore:
      qdrant:
        host: *************
        port: 6334
        #        api-key: sk-5KLF9TlyStmL3-jf-6yX3Q
        collection-name: test_data
        use-tls: false
        initialize-schema: true

agent:
  type: dify
  base-url: http://*************:32104/v1
  api-key: app-fZOxl9rrZpkwENIZoY5R7oxS