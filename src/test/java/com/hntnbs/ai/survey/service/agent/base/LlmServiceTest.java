package com.hntnbs.ai.survey.service.agent.base;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.beans.factory.annotation.Autowired;

@SpringBootTest
@ActiveProfiles("yth-dev")
public class LlmServiceTest {

    @Autowired
    private LlmService llmService;

    @Test
    public void testPlanningChatClientConfiguration() {
        // 测试规划聊天客户端是否正确配置
        ChatClient planningClient = llmService.getPlanningChatClient();
        
        // 创建一个简单的提示来测试配置
        Prompt testPrompt = new Prompt("Hello, this is a test message.");
        
        try {
            // 尝试调用聊天客户端
            var response = planningClient.prompt(testPrompt).call().chatResponse();
            System.out.println("Test successful: " + response.getResult().getOutput().getText());
        } catch (Exception e) {
            System.err.println("Error occurred: " + e.getMessage());
            // 检查是否是enable_thinking相关的错误
            if (e.getMessage().contains("enable_thinking")) {
                System.err.println("This is the enable_thinking parameter error we need to fix.");
            }
        }
    }
}
